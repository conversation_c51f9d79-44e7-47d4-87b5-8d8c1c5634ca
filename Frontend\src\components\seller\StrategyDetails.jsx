import React, { useEffect, useState } from "react";
import { useParams, useNavigate } from "react-router-dom";
import { useDispatch, useSelector } from "react-redux";
import SellerLayout from "./SellerLayout";
import { getSellerContentById, deleteContent } from "../../redux/slices/contentSlice";
import { toast } from "react-toastify";
import "../../styles/StrategyDetails.css";
import ourmissionimage from "../../assets/images/ourmissionimage.svg";
// Icons
import { MdPlayArrow, MdEdit, MdDelete, MdVisibility } from "react-icons/md";
import ConfirmationModal from "../common/ConfirmationModal";
import MediaViewer from "../common/MediaViewer";

const StrategyDetails = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const dispatch = useDispatch();
  const { singleContent, isLoading, error } = useSelector((state) => state.content);

  // State for delete confirmation modal
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);

  // State for media viewer
  const [showMediaViewer, setShowMediaViewer] = useState(false);

  // Fetch content data when component mounts or ID changes
  useEffect(() => {
    if (id) {
      dispatch(getSellerContentById(id));
    }
  }, [dispatch, id]);

  // Refresh data when returning from edit page
  useEffect(() => {
    const handleFocus = () => {
      if (id && document.visibilityState === 'visible') {
        dispatch(getSellerContentById(id));
      }
    };

    document.addEventListener('visibilitychange', handleFocus);
    window.addEventListener('focus', handleFocus);

    return () => {
      document.removeEventListener('visibilitychange', handleFocus);
      window.removeEventListener('focus', handleFocus);
    };
  }, [dispatch, id]);

  // Handle error display
  useEffect(() => {
    if (error) {
      toast.error(error.message || "Failed to load strategy details");
    }
  }, [error]);

  // Handle edit button click
  const handleEdit = () => {
    navigate(`/seller/strategy-details/${id}/edit`);
  };

  // Handle delete button click
  const handleDelete = () => {
    setShowDeleteModal(true);
  };

  // Handle delete confirmation
  const handleDeleteConfirm = async () => {
    setIsDeleting(true);
    try {
      await dispatch(deleteContent(id)).unwrap();
      toast.success("Strategy deleted successfully");
      navigate("/seller/my-sports-strategies");
    } catch (error) {
      console.error("Delete failed:", error);
      toast.error(error.message || "Failed to delete strategy");
    } finally {
      setIsDeleting(false);
      setShowDeleteModal(false);
    }
  };

  // Handle file preview/download
  const handleFilePreview = () => {
    if (singleContent?.fileUrl) {
      setShowMediaViewer(true);
    } else {
      toast.error("No file available for preview");
    }
  };

  // Helper functions for formatting data
  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString("en-US", {
      year: "numeric",
      month: "long",
      day: "numeric",
    });
  };

  const formatPrice = (price) => {
    return typeof price === "number" ? `$${price.toFixed(2)}` : price || "$0.00";
  };

  const formatDuration = (duration) => {
    if (!duration) return "N/A";
    const hours = Math.floor(duration / 60);
    const minutes = duration % 60;
    return hours > 0 ? `${hours}:${minutes.toString().padStart(2, '0')}:00` : `${minutes}:00`;
  };

  // Show loading state
  if (isLoading) {
    return (
      <SellerLayout>
        <div className="StrategyDetails">
          <div className="loading-container">
            <div className="loading-spinner"></div>
            <p>Loading strategy details...</p>
          </div>
        </div>
      </SellerLayout>
    );
  }

  // Show error state if no content found
  if (!singleContent) {
    return (
      <SellerLayout>
        <div className="StrategyDetails">
          <div className="error-container">
            <h3>Strategy not found</h3>
            <p>The strategy you're looking for doesn't exist or has been removed.</p>
            <button
              className="btn btn-primary"
              onClick={() => navigate("/seller/my-sports-strategies")}
            >
              Back to Strategies
            </button>
          </div>
        </div>
      </SellerLayout>
    );
  }

  const strategy = singleContent;

  return (
    <SellerLayout>
      <div className="StrategyDetails">
        {/* Video/Document Info Section */}
        <div className="StrategyDetails__info-header">
          <div className="StrategyDetails__info-content">
            <h2 className="StrategyDetails__info-title">Video/Document Info</h2>
          </div>
          <div className="StrategyDetails__info-actions">
            <button
              className="StrategyDetails__edit-btn"
              onClick={handleEdit}
            >
              <MdEdit className="StrategyDetails__action-icon" />
              Edit
            </button>
            <button
              className="StrategyDetails__delete-btn"
              onClick={handleDelete}
            >
              <MdDelete className="StrategyDetails__action-icon" />
              Delete
            </button>
          </div>
        </div>

        {/* Main Content */}
        <div className="StrategyDetails__content">
          <h3 className="StrategyDetails__strategy-title">{strategy.title}</h3>
          {/* Strategy Image */}
          <div className="StrategyDetails__image-container">
            <img
              src={strategy.thumbnailUrl || strategy.previewUrl || ourmissionimage}
              alt={strategy.title}
              className="StrategyDetails__image"
            />
            <div
              className="StrategyDetails__play-overlay"
              onClick={handleFilePreview}
              title={strategy.fileUrl ? "Preview/Download file" : "No file available"}
            >
              {strategy.fileUrl ? (
                <>
                  <MdVisibility className="StrategyDetails__play-icon" />
                  <span className="StrategyDetails__overlay-text">Preview</span>
                </>
              ) : (
                <MdPlayArrow className="StrategyDetails__play-icon" />
              )}
            </div>
          </div>
          {/* Description Section */}
          <div className="StrategyDetails__description-section">
            <h3 className="StrategyDetails__section-title">Description</h3>
            <div
              className="StrategyDetails__description"
              dangerouslySetInnerHTML={{ __html: strategy.description }}
            />
          </div>
          {/* Coach Info */}
          <div className="StrategyDetails__coach-section">
            <h3 className="StrategyDetails__section-title">The Coach</h3>
            <div className="StrategyDetails__coach-info">
              <div className="StrategyDetails__coach-details">
                <h4 className="StrategyDetails__coach-name">
                  {strategy.coachName || "Coach Name"}
                </h4>
                <p className="StrategyDetails__coach-title">
                  {strategy.sport} Specialist
                </p>
                <div
                  className="StrategyDetails__coach-description"
                  dangerouslySetInnerHTML={{ __html: strategy.aboutCoach }}
                />
              </div>
            </div>
          </div>

          <div className="StrategyDetails__stats-and-steps">
            {/* Key Stats Section */}
            <div className="StrategyDetails__stats-section">
              <h3 className="StrategyDetails__section-title">
                Strategic Content Info
              </h3>
              <div className="StrategyDetails__stats-grid">
                <div className="StrategyDetails__stat-card">
                  <div className="StrategyDetails__stat-content">
                    <span className="StrategyDetails__stat-label">
                      Category
                    </span>
                    <span className="StrategyDetails__stat-value">
                      {strategy.category}
                    </span>
                  </div>
                </div>

                <div className="StrategyDetails__stat-card">
                  <div className="StrategyDetails__stat-content">
                    <span className="StrategyDetails__stat-label">
                      Sport
                    </span>
                    <span className="StrategyDetails__stat-value">
                      {strategy.sport}
                    </span>
                  </div>
                </div>

                <div className="StrategyDetails__stat-card">
                  <div className="StrategyDetails__stat-content">
                    <span className="StrategyDetails__stat-label">
                      Duration
                    </span>
                    <span className="StrategyDetails__stat-value">
                      {formatDuration(strategy.duration)}
                    </span>
                  </div>
                </div>

                <div className="StrategyDetails__stat-card">
                  <div className="StrategyDetails__stat-content">
                    <span className="StrategyDetails__stat-label">Price</span>
                    <span className="StrategyDetails__stat-value">
                      {formatPrice(strategy.price)}
                    </span>
                  </div>
                </div>

                <div className="StrategyDetails__stat-card">
                  <div className="StrategyDetails__stat-content">
                    <span className="StrategyDetails__stat-label">Content Type</span>
                    <span className="StrategyDetails__stat-value">
                      {strategy.contentType}
                    </span>
                  </div>
                </div>

                <div className="StrategyDetails__stat-card">
                  <div className="StrategyDetails__stat-content">
                    <span className="StrategyDetails__stat-label">Difficulty</span>
                    <span className="StrategyDetails__stat-value">
                      {strategy.difficulty}
                    </span>
                  </div>
                </div>

                <div className="StrategyDetails__stat-card">
                  <div className="StrategyDetails__stat-content">
                    <span className="StrategyDetails__stat-label">Status</span>
                    <span className="StrategyDetails__stat-value">
                      {strategy.status}
                    </span>
                  </div>
                </div>

                <div className="StrategyDetails__stat-card">
                  <div className="StrategyDetails__stat-content">
                    <span className="StrategyDetails__stat-label">Created</span>
                    <span className="StrategyDetails__stat-value">
                      {formatDate(strategy.createdAt)}
                    </span>
                  </div>
                </div>
              </div>
            </div>
            <div className="vertical-line"></div>
            {/* Strategy Steps */}
            <div className="StrategyDetails__steps-section">
              <h3 className="StrategyDetails__section-title">
                Strategic Content Details
              </h3>
              <div className="StrategyDetails__steps-list">
                <div className="StrategyDetails__step">
                  <div
                    className="StrategyDetails__step-text"
                    dangerouslySetInnerHTML={{ __html: strategy.strategicContent }}
                  />
                </div>

                {strategy.tags && strategy.tags.length > 0 && (
                  <div className="StrategyDetails__step">
                    <span className="StrategyDetails__step-label">Tags:</span>
                    <span className="StrategyDetails__step-text">
                      {strategy.tags.join(", ")}
                    </span>
                  </div>
                )}

                {strategy.prerequisites && strategy.prerequisites.length > 0 && (
                  <div className="StrategyDetails__step">
                    <span className="StrategyDetails__step-label">Prerequisites:</span>
                    <span className="StrategyDetails__step-text">
                      {strategy.prerequisites.join(", ")}
                    </span>
                  </div>
                )}

                {strategy.learningObjectives && strategy.learningObjectives.length > 0 && (
                  <div className="StrategyDetails__step">
                    <span className="StrategyDetails__step-label">Learning Objectives:</span>
                    <span className="StrategyDetails__step-text">
                      {strategy.learningObjectives.join(", ")}
                    </span>
                  </div>
                )}

                {strategy.equipment && strategy.equipment.length > 0 && (
                  <div className="StrategyDetails__step">
                    <span className="StrategyDetails__step-label">Equipment Needed:</span>
                    <span className="StrategyDetails__step-text">
                      {strategy.equipment.join(", ")}
                    </span>
                  </div>
                )}

                <div className="StrategyDetails__step">
                  <span className="StrategyDetails__step-label">Language:</span>
                  <span className="StrategyDetails__step-text">
                    {strategy.language}
                  </span>
                </div>

                <div className="StrategyDetails__step">
                  <span className="StrategyDetails__step-label">Sale Type:</span>
                  <span className="StrategyDetails__step-text">
                    {strategy.saleType}
                  </span>
                </div>

                {strategy.fileSize && (
                  <div className="StrategyDetails__step">
                    <span className="StrategyDetails__step-label">File Size:</span>
                    <span className="StrategyDetails__step-text">
                      {(strategy.fileSize / 1024 / 1024).toFixed(2)} MB
                    </span>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>

        {/* Delete Confirmation Modal */}
        <ConfirmationModal
          isOpen={showDeleteModal}
          onClose={() => setShowDeleteModal(false)}
          onConfirm={handleDeleteConfirm}
          title="Delete Strategy"
          message={`Are you sure you want to delete "${strategy?.title}"? This action cannot be undone.`}
          confirmText="Delete"
          cancelText="Cancel"
          type="danger"
          isLoading={isDeleting}
        />

        {/* Media Viewer Modal */}
        <MediaViewer
          isOpen={showMediaViewer}
          onClose={() => setShowMediaViewer(false)}
          fileUrl={strategy?.fileUrl}
          fileName={strategy?.title}
          fileType={strategy?.contentType}
          title={strategy?.title}
        />
      </div>
    </SellerLayout>
  );
};

export default StrategyDetails;
