import React from "react";
import { FaTimes, FaExclamationTriangle } from "react-icons/fa";
import "../../styles/ConfirmationModal.css";

const ConfirmationModal = ({
  isOpen,
  onClose,
  onConfirm,
  title = "Confirm Action",
  message = "Are you sure you want to proceed?",
  confirmText = "Confirm",
  cancelText = "Cancel",
  type = "warning", // warning, danger, info
  isLoading = false,
}) => {
  const handleOverlayClick = (e) => {
    if (e.target === e.currentTarget && !isLoading) {
      onClose();
    }
  };

  const handleConfirm = () => {
    if (!isLoading) {
      onConfirm();
    }
  };

  const handleCancel = () => {
    if (!isLoading) {
      onClose();
    }
  };

  if (!isOpen) return null;

  return (
    <div className="confirmation-modal-overlay" onClick={handleOverlayClick}>
      <div className="confirmation-modal">
        <div className="confirmation-modal__header">
          <div className={`confirmation-modal__icon confirmation-modal__icon--${type}`}>
            <FaExclamationTriangle />
          </div>
          <button
            className="confirmation-modal__close"
            onClick={handleCancel}
            disabled={isLoading}
          >
            <FaTimes />
          </button>
        </div>

        <div className="confirmation-modal__content">
          <h3 className="confirmation-modal__title">{title}</h3>
          <p className="confirmation-modal__message">{message}</p>
        </div>

        <div className="confirmation-modal__actions">
          <button
            className="confirmation-modal__cancel"
            onClick={handleCancel}
            disabled={isLoading}
          >
            {cancelText}
          </button>
          <button
            className={`confirmation-modal__confirm confirmation-modal__confirm--${type}`}
            onClick={handleConfirm}
            disabled={isLoading}
          >
            {isLoading ? "Processing..." : confirmText}
          </button>
        </div>
      </div>
    </div>
  );
};

export default ConfirmationModal;
