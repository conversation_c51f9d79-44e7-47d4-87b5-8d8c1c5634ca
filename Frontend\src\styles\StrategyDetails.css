/* StrategyDetails Component Styles */
.StrategyDetails {
  padding: 0;
  background-color: var(--white);
  font-family: "Poppins", sans-serif;
  color: var(--text-color);
}

/* Video/Document Info Header Section */
.StrategyDetails .StrategyDetails__info-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--heading5) 0;
  border-bottom: 1px solid var(--light-gray);
  margin-bottom: var(--heading4);
}

.StrategyDetails .StrategyDetails__info-content {
  flex: 1;
}

.StrategyDetails .StrategyDetails__info-title {
  font-size: var(--heading6);
  color: var(--secondary-color);
  font-weight: 600;
}

.StrategyDetails .StrategyDetails__strategy-title {
  font-size: var(--basefont);
  color: var(--text-color);
  font-weight: 500;
  margin: 0;
  line-height: 1.4;
}

.StrategyDetails .StrategyDetails__info-actions {
  display: flex;
  gap: var(--smallfont);
  align-items: center;
}

.StrategyDetails .StrategyDetails__edit-btn,
.StrategyDetails .StrategyDetails__delete-btn {
  display: flex;
  align-items: center;
  gap: var(--extrasmallfont);
  padding: var(--extrasmallfont) var(--smallfont);
  border: 1px solid;
  border-radius: var(--border-radius);
  font-size: var(--smallfont);
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  background-color: transparent;
}

.StrategyDetails .StrategyDetails__edit-btn {
  color: #2e8e41;
  border-color: #2e8e41;
}

.StrategyDetails .StrategyDetails__edit-btn:hover {
  background-color: #2e8e41;
  color: var(--white);
}

.StrategyDetails .StrategyDetails__delete-btn {
  color: #dc3545;
  border-color: #dc3545;
}

.StrategyDetails .StrategyDetails__delete-btn:hover {
  background-color: #dc3545;
  color: var(--white);
}

.StrategyDetails .StrategyDetails__action-icon {
  font-size: var(--basefont);
}

/* Header Section - Using Seller Layout Style */
.StrategyDetails .StrategyDetails__header {
  margin-bottom: var(--heading4);
  border-bottom: 1px solid #fddcdc;
  padding-bottom: var(--basefont);
}

.StrategyDetails .StrategyDetails__title-container {
  margin-bottom: var(--smallfont);
}

.StrategyDetails .StrategyDetails__title {
  display: inline-flex;
  align-items: center;
  background-color: #fddcdc;
  color: #0a0033;
  padding: var(--smallfont) var(--basefont);
  border-top-left-radius: var(--border-radius);
  border-top-right-radius: var(--heading5);
  border-bottom-left-radius: 0;
  position: relative;
  font-weight: 600;
  font-family: sans-serif;
  clip-path: polygon(0 0, 95% 0, 100% 100%, 0% 100%);
  font-size: var(--heading5);
  margin: 0;
  gap: var(--smallfont);
  line-height: 1.3;
}

.StrategyDetails .StrategyDetails__subtitle {
  font-size: var(--basefont);
  color: var(--dark-gray);
  margin: 0;
  font-weight: 500;
}

/* Main Content */
.StrategyDetails .StrategyDetails__content {
  display: flex;
  flex-direction: column;
  gap: var(--heading4);
}

/* Image Section */
.StrategyDetails .StrategyDetails__image-container {
  position: relative;
  width: 100%;
  border-radius: var(--border-radius-large);
  overflow: hidden;
  box-shadow: var(--box-shadow);
}

.StrategyDetails .StrategyDetails__image {
  width: 100%;
  height: auto;
  max-height: 450px;
  object-fit: cover;
  display: block;
}

.StrategyDetails .StrategyDetails__play-overlay {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background-color: rgba(238, 52, 37, 0.9);
  border-radius: 50%;
  width: var(--heading1);
  height: var(--heading1);
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
}

.StrategyDetails .StrategyDetails__play-overlay:hover {
  background-color: var(--primary-color);
  transform: translate(-50%, -50%) scale(1.1);
}

.StrategyDetails .StrategyDetails__play-icon {
  color: var(--white);
  font-size: var(--heading5);
}

/* Section Titles */
.StrategyDetails .StrategyDetails__section-title {
  font-size: var(--heading6);
  color: var(--text-color);
  font-weight: 600;
  margin: 0 0 var(--basefont) 0;
  border-bottom: 2px solid var(--primary-light-color);
  padding-bottom: var(--smallfont);
}

/* Coach Section */
.StrategyDetails .StrategyDetails__coach-section {
  padding: var(--heading5);
  border-radius: var(--border-radius-large);
}

.StrategyDetails .StrategyDetails__coach-info {
  display: flex;
  flex-direction: column;
  gap: var(--basefont);
}

.StrategyDetails .StrategyDetails__coach-name {
  font-size: var(--heading6);
  color: var(--text-color);
  font-weight: 600;
  margin: 0 0 var(--extrasmallfont) 0;
}

.StrategyDetails .StrategyDetails__coach-title {
  font-size: var(--basefont);
  color: var(--btn-color);
  font-weight: 500;
  margin: 0 0 var(--basefont) 0;
}

.StrategyDetails .StrategyDetails__coach-description {
  font-size: var(--basefont);
  color: var(--dark-gray);
  line-height: 1.6;
  margin: 0;
}

/* Stats Section */
.StrategyDetails .StrategyDetails__stats-section {
  background-color: var(--white);
  padding: var(--heading5);
  border-radius: var(--border-radius-large);
}

.StrategyDetails .StrategyDetails__stats-grid {
  display: grid;

  gap: var(--basefont);
}

.StrategyDetails .StrategyDetails__stat-card {
  display: flex;
  align-items: center;
  gap: var(--basefont);
  padding: var(--basefont);
}

.StrategyDetails .StrategyDetails__stat-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: var(--heading3);
  height: var(--heading3);
  background-color: var(--primary-light-color);
  border-radius: 50%;
  color: var(--btn-color);
  font-size: var(--heading6);
  flex-shrink: 0;
}

.StrategyDetails .StrategyDetails__stat-content {
  display: flex;
  flex-direction: column;
  gap: var(--extrasmallfont);
}

.StrategyDetails .StrategyDetails__stat-label {
  font-size: var(--smallfont);
  color: var(--dark-gray);
  font-weight: 500;
}

.StrategyDetails .StrategyDetails__stat-value {
  font-size: var(--basefont);
  color: var(--text-color);
  font-weight: 600;
}

/* Description Section */
.StrategyDetails .StrategyDetails__description-section {
  background-color: var(--white);
  padding: var(--heading5);
  border-radius: var(--border-radius-large);
}

.StrategyDetails .StrategyDetails__description {
  font-size: var(--basefont);
  color: var(--text-color);
  line-height: 1.7;
  margin: 0;
}

/* HTML Content Styling */
.StrategyDetails .StrategyDetails__description p,
.StrategyDetails .StrategyDetails__coach-description p,
.StrategyDetails .StrategyDetails__step-text p {
  margin: 0 0 var(--smallfont) 0;
  line-height: 1.6;
}

.StrategyDetails .StrategyDetails__description p:last-child,
.StrategyDetails .StrategyDetails__coach-description p:last-child,
.StrategyDetails .StrategyDetails__step-text p:last-child {
  margin-bottom: 0;
}

.StrategyDetails .StrategyDetails__description strong,
.StrategyDetails .StrategyDetails__coach-description strong,
.StrategyDetails .StrategyDetails__step-text strong {
  color: var(--btn-color);
  font-weight: 600;
}

.StrategyDetails .StrategyDetails__description em,
.StrategyDetails .StrategyDetails__coach-description em,
.StrategyDetails .StrategyDetails__step-text em {
  font-style: italic;
  color: var(--dark-gray);
}

.StrategyDetails .StrategyDetails__description ul,
.StrategyDetails .StrategyDetails__coach-description ul,
.StrategyDetails .StrategyDetails__step-text ul {
  margin: var(--smallfont) 0;
  padding-left: var(--heading5);
}

.StrategyDetails .StrategyDetails__description li,
.StrategyDetails .StrategyDetails__coach-description li,
.StrategyDetails .StrategyDetails__step-text li {
  margin-bottom: var(--extrasmallfont);
  line-height: 1.5;
}

.StrategyDetails .StrategyDetails__description ol,
.StrategyDetails .StrategyDetails__coach-description ol,
.StrategyDetails .StrategyDetails__step-text ol {
  margin: var(--smallfont) 0;
  padding-left: var(--heading5);
}

.StrategyDetails .StrategyDetails__description h1,
.StrategyDetails .StrategyDetails__description h2,
.StrategyDetails .StrategyDetails__description h3,
.StrategyDetails .StrategyDetails__description h4,
.StrategyDetails .StrategyDetails__description h5,
.StrategyDetails .StrategyDetails__description h6,
.StrategyDetails .StrategyDetails__coach-description h1,
.StrategyDetails .StrategyDetails__coach-description h2,
.StrategyDetails .StrategyDetails__coach-description h3,
.StrategyDetails .StrategyDetails__coach-description h4,
.StrategyDetails .StrategyDetails__coach-description h5,
.StrategyDetails .StrategyDetails__coach-description h6,
.StrategyDetails .StrategyDetails__step-text h1,
.StrategyDetails .StrategyDetails__step-text h2,
.StrategyDetails .StrategyDetails__step-text h3,
.StrategyDetails .StrategyDetails__step-text h4,
.StrategyDetails .StrategyDetails__step-text h5,
.StrategyDetails .StrategyDetails__step-text h6 {
  color: var(--text-color);
  font-weight: 600;
  margin: var(--basefont) 0 var(--smallfont) 0;
  line-height: 1.3;
}

.StrategyDetails .StrategyDetails__description h1,
.StrategyDetails .StrategyDetails__coach-description h1,
.StrategyDetails .StrategyDetails__step-text h1 {
  font-size: var(--heading5);
}

.StrategyDetails .StrategyDetails__description h2,
.StrategyDetails .StrategyDetails__coach-description h2,
.StrategyDetails .StrategyDetails__step-text h2 {
  font-size: var(--heading6);
}

.StrategyDetails .StrategyDetails__description h3,
.StrategyDetails .StrategyDetails__coach-description h3,
.StrategyDetails .StrategyDetails__step-text h3 {
  font-size: var(--basefont);
}

.StrategyDetails .StrategyDetails__description h4,
.StrategyDetails .StrategyDetails__description h5,
.StrategyDetails .StrategyDetails__description h6,
.StrategyDetails .StrategyDetails__coach-description h4,
.StrategyDetails .StrategyDetails__coach-description h5,
.StrategyDetails .StrategyDetails__coach-description h6,
.StrategyDetails .StrategyDetails__step-text h4,
.StrategyDetails .StrategyDetails__step-text h5,
.StrategyDetails .StrategyDetails__step-text h6 {
  font-size: var(--smallfont);
}
.StrategyDetails .StrategyDetails__stats-and-steps {
  display: grid;

  justify-content: space-between;
  gap: 1rem;
  grid-template-columns: 1fr 1px 1fr;
}
/* Steps Section */
.StrategyDetails .StrategyDetails__steps-section {
  background-color: var(--white);
  padding: var(--heading5);
  border-radius: var(--border-radius-large);
}

.StrategyDetails .StrategyDetails__steps-list {
  display: flex;
  flex-direction: column;
  gap: var(--basefont);
}

.StrategyDetails .StrategyDetails__step {
  display: flex;
  flex-direction: column;
  gap: var(--extrasmallfont);
  padding: var(--basefont);
  border-bottom: 1px solid var(--light-gray);
  transition: all 0.3s ease;
}

.StrategyDetails .StrategyDetails__step:last-child {
  border-bottom: none;
}

.StrategyDetails .StrategyDetails__step-label {
  font-size: var(--smallfont);
  color: var(--btn-color);
  font-weight: 600;
  margin-bottom: var(--extrasmallfont);
}

.StrategyDetails .StrategyDetails__step-text {
  font-size: var(--basefont);
  color: var(--text-color);
  font-weight: 400;
  line-height: 1.5;
}

/* Loading and Error States */
.StrategyDetails .loading-container,
.StrategyDetails .error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  text-align: center;
}

.StrategyDetails .loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid var(--light-gray);
  border-top: 4px solid var(--primary-color);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 16px;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

.StrategyDetails .loading-container p,
.StrategyDetails .error-container p {
  color: var(--text-color);
  font-size: var(--basefont);
  margin: 0 0 16px 0;
}

.StrategyDetails .error-container h3 {
  color: var(--text-color);
  font-size: var(--heading5);
  margin: 0 0 12px 0;
  font-weight: 600;
}

.StrategyDetails .btn {
  padding: 12px 24px;
  border-radius: var(--border-radius);
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  border: none;
  font-size: var(--basefont);
  text-decoration: none;
  display: inline-block;
}

.StrategyDetails .btn-primary {
  background: var(--btn-color);
  color: white;
}

.StrategyDetails .btn-primary:hover {
  background: var(--primary-color);
}
/* CTA Section */

/* Responsive Design */
@media (max-width: 768px) {
  .StrategyDetails .StrategyDetails__info-header {
    flex-direction: column;
    gap: var(--basefont);
    align-items: stretch;
  }

  .StrategyDetails .StrategyDetails__info-actions {
    justify-content: flex-start;
  }

  .StrategyDetails .StrategyDetails__info-title {
    font-size: var(--basefont);
  }

  .StrategyDetails .StrategyDetails__strategy-title {
    font-size: var(--smallfont);
  }

  .StrategyDetails .StrategyDetails__edit-btn,
  .StrategyDetails .StrategyDetails__delete-btn {
    padding: var(--extrasmallfont) var(--extrasmallfont);
    font-size: var(--extrasmallfont);
  }

  .StrategyDetails .StrategyDetails__title {
    font-size: var(--heading6);
    padding: var(--extrasmallfont) var(--smallfont);
    gap: var(--extrasmallfont);
  }

  .StrategyDetails .StrategyDetails__subtitle {
    font-size: var(--smallfont);
  }

  .StrategyDetails .StrategyDetails__content {
    gap: var(--heading5);
  }

  .StrategyDetails .StrategyDetails__play-overlay {
    width: var(--heading3);
    height: var(--heading3);
  }

  .StrategyDetails .StrategyDetails__play-icon {
    font-size: var(--heading6);
  }

  .StrategyDetails .StrategyDetails__section-title {
    font-size: var(--basefont);
  }

  .StrategyDetails .StrategyDetails__coach-section,
  .StrategyDetails .StrategyDetails__stats-section,
  .StrategyDetails .StrategyDetails__description-section,
  .StrategyDetails .StrategyDetails__steps-section {
    padding: var(--basefont);
  }

  .StrategyDetails .StrategyDetails__stats-grid {
    grid-template-columns: 1fr;
    gap: var(--smallfont);
  }

  .StrategyDetails .StrategyDetails__stat-card {
    padding: var(--smallfont);
    gap: var(--smallfont);
  }

  .StrategyDetails .StrategyDetails__stat-icon {
    width: var(--heading5);
    height: var(--heading5);
    font-size: var(--basefont);
  }

  .StrategyDetails .StrategyDetails__coach-name {
    font-size: var(--basefont);
  }

  .StrategyDetails .StrategyDetails__coach-title,
  .StrategyDetails .StrategyDetails__coach-description,
  .StrategyDetails .StrategyDetails__description,
  .StrategyDetails .StrategyDetails__step-text {
    font-size: var(--smallfont);
  }

  .StrategyDetails .StrategyDetails__step {
    padding: var(--smallfont);
    gap: var(--smallfont);
  }
}

@media (max-width: 480px) {
  .StrategyDetails .StrategyDetails__info-header {
    padding: var(--basefont) 0;
  }

  .StrategyDetails .StrategyDetails__info-title {
    font-size: var(--smallfont);
  }

  .StrategyDetails .StrategyDetails__strategy-title {
    font-size: var(--extrasmallfont);
  }

  .StrategyDetails .StrategyDetails__info-actions {
    gap: var(--extrasmallfont);
  }

  .StrategyDetails .StrategyDetails__edit-btn,
  .StrategyDetails .StrategyDetails__delete-btn {
    padding: 4px 8px;
    font-size: var(--extrasmallfont);
  }

  .StrategyDetails .StrategyDetails__action-icon {
    font-size: var(--smallfont);
  }

  .StrategyDetails .StrategyDetails__title {
    font-size: var(--basefont);
    padding: var(--extrasmallfont) var(--smallfont);
  }

  .StrategyDetails .StrategyDetails__subtitle {
    font-size: var(--extrasmallfont);
  }

  .StrategyDetails .StrategyDetails__content {
    gap: var(--basefont);
  }

  .StrategyDetails .StrategyDetails__coach-section,
  .StrategyDetails .StrategyDetails__stats-section,
  .StrategyDetails .StrategyDetails__description-section,
  .StrategyDetails .StrategyDetails__steps-section {
    padding: var(--smallfont);
  }

  .StrategyDetails .StrategyDetails__section-title {
    font-size: var(--smallfont);
    margin-bottom: var(--smallfont);
  }

  .StrategyDetails .StrategyDetails__play-overlay {
    width: var(--heading5);
    height: var(--heading5);
  }

  .StrategyDetails .StrategyDetails__play-icon {
    font-size: var(--basefont);
  }

  .StrategyDetails .StrategyDetails__stat-card {
    padding: var(--extrasmallfont);
  }

  .StrategyDetails .StrategyDetails__stat-icon {
    width: var(--basefont);
    height: var(--basefont);
    font-size: var(--smallfont);
  }

  .StrategyDetails .StrategyDetails__stat-label,
  .StrategyDetails .StrategyDetails__stat-value {
    font-size: var(--extrasmallfont);
  }

  .StrategyDetails .StrategyDetails__coach-name,
  .StrategyDetails .StrategyDetails__coach-title,
  .StrategyDetails .StrategyDetails__coach-description,
  .StrategyDetails .StrategyDetails__description,
  .StrategyDetails .StrategyDetails__step-text {
    font-size: var(--extrasmallfont);
  }
}
.StrategyDetails .vertical-line {
  display: flex;
  height: 100%;
  background-color: var(--light-gray);
  align-items: center;
}
